# 版本管理系统说明

## 概述
为ESP32-C3 BLE Mill Monitor Slave项目添加了完整的版本管理系统，支持在系统启动时显示详细的版本信息。

## 文件结构

### 1. `main/version.h` - 版本定义头文件
包含所有版本相关的宏定义：
- 软件版本号（主版本.次版本.补丁版本-构建号）
- 项目信息（名称、描述、构建时间）
- 硬件版本信息
- 功能模块版本

### 2. `main/version.c` - 版本信息实现
实现版本信息打印和获取函数：
- `print_version_info()` - 打印完整版本信息
- `get_version_string()` - 获取版本字符串
- `get_build_info()` - 获取构建信息

### 3. `main/main.c` - 主程序集成
在系统启动时调用 `print_version_info()` 显示版本信息。

## 版本号格式

### 当前版本：v1.0.0-build1

**版本号组成：**
- **主版本号 (Major)**：1 - 重大功能变更或不兼容更新
- **次版本号 (Minor)**：0 - 新功能添加，向后兼容
- **补丁版本号 (Patch)**：0 - Bug修复，向后兼容
- **构建号 (Build)**：1 - 每次构建递增

## 如何更新版本

### 修改 `main/version.h` 文件中的宏定义：

```c
// 版本号定义
#define SOFTWARE_VERSION_MAJOR    1    // 主版本号
#define SOFTWARE_VERSION_MINOR    0    // 次版本号  
#define SOFTWARE_VERSION_PATCH    0    // 补丁版本号
#define SOFTWARE_VERSION_BUILD    1    // 构建号

// 版本字符串（需要手动同步更新）
#define SOFTWARE_VERSION_STRING   "v1.0.0-build1"
```

### 版本更新示例：

1. **Bug修复**：1.0.0-build1 → 1.0.1-build1
2. **新功能**：1.0.1-build1 → 1.1.0-build1  
3. **重大更新**：1.1.0-build1 → 2.0.0-build1
4. **构建更新**：1.0.0-build1 → 1.0.0-build2

## 启动时显示的信息

系统启动时会显示：
```
========================================
  BLE Mill Monitor Slave
  ESP32-C3 BLE传感器监控从机
========================================
Software Version: v1.0.0-build1
Build Date: Dec 20 2024 14:30:25
----------------------------------------
Hardware Information:
  Chip Model: ESP32-C3
  Board Version: v1.0
  CPU Cores: 1
  Flash Size: 4 MB
  Chip Features: WiFi/BLE
----------------------------------------
Module Versions:
  BLE: 5.0
  MS5837 Sensor: v1.1-fixed
  OTA Update: v1.0
========================================
```

## API使用

### 在代码中获取版本信息：

```c
#include "version.h"

// 打印完整版本信息
print_version_info();

// 获取版本字符串
const char* version = get_version_string();
printf("当前版本: %s\n", version);

// 获取构建信息
const char* build_info = get_build_info();
printf("构建信息: %s\n", build_info);
```

## 编译集成

版本文件已添加到 `main/CMakeLists.txt` 中：
```cmake
idf_component_register(SRCS
    ...
    "version.c"
    "main.c"
    ...
)
```

## 维护建议

1. **每次发布前更新版本号**
2. **构建号可以与CI/CD系统集成自动递增**
3. **重要更新时更新功能模块版本**
4. **保持版本字符串与数字版本同步**

## 未来扩展

可以考虑添加：
- Git提交哈希值
- 编译器版本信息
- 依赖库版本
- 自动版本号生成脚本
