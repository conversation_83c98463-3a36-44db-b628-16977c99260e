/*
 * 软件版本定义
 * BLE Mill Monitor Slave - ESP32-C3
 */

#pragma once

#ifdef __cplusplus
extern "C" {
#endif

// 版本字符串
#define SOFTWARE_VERSION_STRING   "t-0.4"

// 项目信息
#define PROJECT_NAME             "Jar Monitor Slave"
#define BUILD_DATE               __DATE__
#define BUILD_TIME               __TIME__


/**
 * @brief 打印完整的版本信息
 */
void print_version_info(void);

/**
 * @brief 获取版本字符串
 * @return 版本字符串指针
 */
const char* get_version_string(void);

/**
 * @brief 获取构建信息字符串
 * @return 构建信息字符串指针
 */
const char* get_build_info(void);

#ifdef __cplusplus
}
#endif
