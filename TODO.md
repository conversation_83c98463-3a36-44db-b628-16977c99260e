# GPIO监控功能实现计划

## 需求分析
- 监控 GPIO_POWER_SOURCE (GPIO_NUM_10) 和 GPIO_CHARGE_STATUS (GPIO_NUM_18)
- LED行为逻辑：
  - 当 GPIO_POWER_SOURCE 为低电平（外部供电）时：
    - 如果 GPIO_CHARGE_STATUS 为低电平：LED常亮
    - 否则：LED闪烁（500毫秒间隔）

## 实现步骤

### [ ] 步骤1: 修改 led_charge.h
- [ ] 添加GPIO状态读取函数声明
- [ ] 添加电源状态和充电状态的枚举定义

### [ ] 步骤2: 修改 led_charge.c 
- [ ] 在 led_charge_init() 中添加GPIO_POWER_SOURCE和GPIO_CHARGE_STATUS的输入配置
- [ ] 添加读取GPIO状态的函数
- [ ] 修改 led_charge_task_callback() 实现监控逻辑和LED控制

### [ ] 步骤3: 验证和测试
- [ ] 确保初始化正确
- [ ] 验证GPIO监控功能
- [ ] 测试LED行为是否符合预期

## 当前状态
- [x] 分析现有代码结构
- [x] 确定实现方案
- [x] 开始实现

## 已完成的步骤

### [x] 步骤1: 修改 led_charge.h
- [x] 添加GPIO状态读取函数声明
- [x] 添加电源状态和充电状态的枚举定义

### [x] 步骤2: 修改 led_charge.c 
- [x] 在 led_charge_init() 中添加GPIO_POWER_SOURCE和GPIO_CHARGE_STATUS的输入配置
- [x] 添加读取GPIO状态的函数
- [x] 修改 led_charge_task_callback() 实现监控逻辑和LED控制

### [x] 步骤3: 修改 main.c
- [x] 更新初始化调用，使用led_charge_init()和led_charge_task()

### [ ] 步骤4: 验证和测试
- [ ] 确保初始化正确
- [ ] 验证GPIO监控功能
- [ ] 测试LED行为是否符合预期
