#pragma once

#include "sdkconfig.h"
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/i2c_master.h"
#include "esp_log.h"

// MS5837-30BA Commands
#define MS5837_CMD_RESET 0x1E
#define MS5837_CMD_CONV_D1_256 0x40
#define MS5837_CMD_CONV_D1_512 0x42
#define MS5837_CMD_CONV_D1_1024 0x44
#define MS5837_CMD_CONV_D1_2048 0x46
#define MS5837_CMD_CONV_D1_4096 0x48
#define MS5837_CMD_CONV_D1_8192 0x4A
#define MS5837_CMD_CONV_D2_256 0x50
#define MS5837_CMD_CONV_D2_512 0x52
#define MS5837_CMD_CONV_D2_1024 0x54
#define MS5837_CMD_CONV_D2_2048 0x56
#define MS5837_CMD_CONV_D2_4096 0x58
#define MS5837_CMD_CONV_D2_8192 0x5A
#define MS5837_CMD_ADC_READ 0x00
#define MS5837_CMD_PROM_READ 0xA0

// PROM addresses - MS5837 has 8 PROM words:
// 0: CRC + Factory defined, 1-6: C1-C6 calibration coefficients, 7: Subsidiary value (set to 0)
#define MS5837_PROM_SIZE 8

#ifdef __cplusplus
extern "C"
{
#endif

    typedef struct
    {
        i2c_master_dev_handle_t dev_handle;
        uint16_t prom[MS5837_PROM_SIZE];
        bool initialized;
    } ms5837_t;

    esp_err_t ms5837_init(ms5837_t *sensor);
    esp_err_t ms5837_get_data(ms5837_t *sensor, float *temperature, float *pressure);
    esp_err_t ms5837_reset(ms5837_t *sensor);
    esp_err_t ms5837_read_prom(ms5837_t *sensor);
    uint8_t ms5837_crc4(uint16_t n_prom[]);

#ifdef __cplusplus
}
#endif